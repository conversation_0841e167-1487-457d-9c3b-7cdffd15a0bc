#!/usr/bin/env python3
"""
Script para estimação de valores da média OHLC usando XGBoost
Utiliza exatamente as mesmas features do classificador XGBoost mas prediz valores
ao invés de classes. Os sinais de compra/venda são gerados baseados no valor estimado
comparado com o valor anterior usando o mesmo threshold do classificador.
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import seaborn as sns
import pickle
import warnings
from scipy.stats import norm
import random

warnings.filterwarnings('ignore')

# Fixar seeds para reprodutibilidade completa
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)
os.environ['PYTHONHASHSEED'] = str(RANDOM_SEED)

# ============================================================================
# FUNÇÕES DE CACHE DE MODELOS
# ============================================================================

def verificar_modelo_estimador_treinado_hoje(modelo_dir, modelo_nome):
    """
    Verifica se o modelo estimador já foi treinado hoje
    """
    modelo_path = os.path.join(modelo_dir, modelo_nome)
    if not os.path.exists(modelo_path):
        return False, None

    # Verificar data de modificação do arquivo
    timestamp_arquivo = os.path.getmtime(modelo_path)
    data_arquivo = datetime.fromtimestamp(timestamp_arquivo).date()
    data_hoje = datetime.now().date()

    if data_arquivo == data_hoje:
        print(f"✅ Modelo estimador {modelo_nome} já foi treinado hoje ({data_hoje})")
        return True, modelo_path
    else:
        print(f"⏰ Modelo estimador {modelo_nome} foi treinado em {data_arquivo}, retreinando...")
        return False, None

def carregar_modelo_estimador_existente(modelo_path):
    """
    Carrega um modelo estimador existente do disco
    """
    try:
        with open(modelo_path, 'rb') as f:
            dados_modelo = pickle.load(f)

        print(f"📦 Modelo estimador carregado com sucesso:")
        print(f"   • R²: {dados_modelo.get('r2', 'N/A'):.3f}")
        print(f"   • RMSE: R$ {dados_modelo.get('rmse', 'N/A'):.4f}")
        print(f"   • Features: {len(dados_modelo.get('feature_cols', []))}")

        return dados_modelo
    except Exception as e:
        print(f"❌ Erro ao carregar modelo estimador: {e}")
        return None

def salvar_modelo_estimador(resultados_modelo, feature_cols):
    """
    Salva o modelo estimador treinado
    """
    modelo_dir = 'results/models/xgboost_estimador_analysis'
    os.makedirs(modelo_dir, exist_ok=True)

    modelo_path = os.path.join(modelo_dir, 'modelo_estimador.pkl')

    dados_para_salvar = {
        'modelo': resultados_modelo['modelo'],
        'scaler': resultados_modelo['scaler'],
        'feature_cols': feature_cols,
        'r2': resultados_modelo['r2'],
        'rmse': resultados_modelo['rmse'],
        'mae': resultados_modelo['mae'],
        'feature_importance': resultados_modelo['feature_importance'],
        # Salvar dados de teste para gráficos
        'y_test': resultados_modelo.get('y_test'),
        'y_pred': resultados_modelo.get('y_pred'),
        'config_usado': {
            'data_period': config.get('xgboost.data_period'),
            'ohlc_lags': config.get('xgboost.features.ohlc_lags'),
            'model_params': config.get('xgboost.model_params')
        }
    }

    with open(modelo_path, 'wb') as f:
        pickle.dump(dados_para_salvar, f)

    print(f"💾 Modelo estimador salvo em: {modelo_path}")

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment

# Importar cache otimizado se disponível
try:
    from cache_optimized import optimized_cache
    CACHE_OPTIMIZED_AVAILABLE = True
except ImportError:
    CACHE_OPTIMIZED_AVAILABLE = False

# Importar cache unificado se disponível
try:
    from cache_unified import unified_cache
    CACHE_UNIFIED_AVAILABLE = True
except ImportError:
    CACHE_UNIFIED_AVAILABLE = False

# Importar funções de features do módulo compartilhado
from features_xgboost import calcular_features_e_sinais

# Importar funções do classificador para reutilizar lógica de dados
from classificador_xgboost_sinais import (
    carregar_acoes_diversificadas,
    baixar_dados_todas_acoes_unificado,
    baixar_dados_acao_otimizado,
    baixar_dados_acao,
    verificar_status_cache,
    limpar_cache_historico
)


def preparar_dataset_regressao(acoes_dados):
    """
    Prepara dataset combinado de todas as ações para treinamento de regressão
    Preserva o índice de data para divisão temporal
    Target: variação percentual da média OHLC em relação ao dia anterior
    """
    datasets = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 50:
            # Adicionar coluna do ticker para identificação
            dados_copy = dados.copy()
            dados_copy['Ticker'] = ticker
            # Resetar índice para preservar as datas como coluna
            dados_copy = dados_copy.reset_index()
            datasets.append(dados_copy)

    # Combinar todos os datasets preservando as datas
    dataset_completo = pd.concat(datasets, ignore_index=True)

    # Converter coluna Date para datetime se necessário
    if 'Date' in dataset_completo.columns:
        dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])
    elif dataset_completo.index.name == 'Date' or isinstance(dataset_completo.index, pd.DatetimeIndex):
        dataset_completo = dataset_completo.reset_index()
        dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])

    # Features: usar exatamente as mesmas features do classificador
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    econometric_lags = config.get('xgboost.features.econometric_lags')

    # Features básicas (REMOVIDAS features do dia atual que usam OHLCV)
    # Mantém apenas features lagged e features temporais
    weekday_features = ['Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta']
    month_features = [f'Mes_{i}' for i in range(1, 13)]
    quarter_features = ['Quarter_1', 'Quarter_2', 'Quarter_3', 'Quarter_4', 'Last_Day_Quarter']
    holiday_features = ['Pre_Feriado_Brasil']

    # NOVA LÓGICA: Features individuais de OHLC ao invés da média
    # Usar features lagged dos componentes individuais de OHLC
    ohlc_individual_features = []
    for i in range(1, ohlc_lags + 1):
        ohlc_individual_features.extend([
            f'Open_PctChange_Lag_{i}',
            f'High_PctChange_Lag_{i}',
            f'Low_PctChange_Lag_{i}',
            f'Close_PctChange_Lag_{i}'
        ])

    # Manter também a média OHLC para compatibilidade (pode ser removida depois se não melhorar performance)
    media_ohlc_features = [f'Media_OHLC_PctChange_Lag_{i}' for i in range(1, ohlc_lags + 1)]

    basic_features = ohlc_individual_features + media_ohlc_features + weekday_features + month_features + quarter_features + holiday_features

    # Features econométricas lagged (APENAS versões lagged)
    econometric_features_all = [
        'Volume', 'Spread', 'Volatilidade',
        'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO',
        'High_Max_50', 'Low_Min_50'
    ]

    econometric_features_lagged = []
    for feature in econometric_features_all:
        for i in range(1, econometric_lags + 1):
            econometric_features_lagged.append(f'{feature}_Lag_{i}')

    # Combinar apenas features lagged e temporais (SEM features do dia atual)
    feature_cols = basic_features + econometric_features_lagged

    X = dataset_completo[feature_cols]

    # NOVO TARGET: Variação percentual da média OHLC em relação ao dia anterior
    # Calcular variação percentual: (valor_hoje - valor_ontem) / valor_ontem * 100
    dataset_completo = dataset_completo.sort_values(['Ticker', 'Date'])
    dataset_completo['Media_OHLC_Anterior'] = dataset_completo.groupby('Ticker')['Media_OHLC'].shift(1)

    # Calcular variação percentual
    mask_valido = (dataset_completo['Media_OHLC_Anterior'] > 0) & (~dataset_completo['Media_OHLC_Anterior'].isna())
    dataset_completo['Target_PctChange'] = np.nan
    dataset_completo.loc[mask_valido, 'Target_PctChange'] = (
        (dataset_completo.loc[mask_valido, 'Media_OHLC'] - dataset_completo.loc[mask_valido, 'Media_OHLC_Anterior']) /
        dataset_completo.loc[mask_valido, 'Media_OHLC_Anterior'] * 100
    )

    # Target: variação percentual
    y = dataset_completo['Target_PctChange']

    # Remover registros com NaN no target
    mask_validos = ~y.isna()
    X_filtrado = X[mask_validos].copy()
    y_filtrado = y[mask_validos].copy()
    dataset_filtrado = dataset_completo[mask_validos].copy()

    print(f"📊 Dataset de regressão preparado:")
    print(f"   • Total de registros: {len(y_filtrado)}")
    print(f"   • Features utilizadas: {len(feature_cols)}")
    print(f"   • Target: Variação % OHLC (min: {y_filtrado.min():.2f}%, max: {y_filtrado.max():.2f}%, média: {y_filtrado.mean():.2f}%)")

    return X_filtrado, y_filtrado, feature_cols, dataset_filtrado


def dividir_dados_temporal_regressao(dataset_completo, feature_cols, y):
    """
    Divide os dados temporalmente baseado na configuração para regressão
    """
    # Ordenar por data
    dataset_ordenado = dataset_completo.sort_values('Date').copy()
    y_ordenado = y[dataset_ordenado.index]
    test_size = config.get('xgboost.test_size')

    # Obter configurações de divisão temporal
    data_max = dataset_ordenado['Date'].max()
    data_min = dataset_ordenado['Date'].min()
    years_range = data_max.year - data_min.year
    test_years = int(years_range * test_size)
    data_corte = data_max - pd.DateOffset(years=test_years)

    print(f"📅 Divisão temporal dos dados (regressão):")
    print(f"   • Data máxima: {data_max.strftime('%Y-%m-%d')}")
    print(f"   • Data de corte: {data_corte.strftime('%Y-%m-%d')}")
    print(f"   • Configuração: {test_years} ano(s) teste")

    # Dividir dados
    mask_treino = dataset_ordenado['Date'] <= data_corte
    mask_teste = dataset_ordenado['Date'] > data_corte

    dados_treino = dataset_ordenado[mask_treino]
    dados_teste = dataset_ordenado[mask_teste]

    print(f"   • Dados de treino: {len(dados_treino)} registros ({dados_treino['Date'].min().strftime('%Y-%m-%d')} a {dados_treino['Date'].max().strftime('%Y-%m-%d')})")
    print(f"   • Dados de teste: {len(dados_teste)} registros ({dados_teste['Date'].min().strftime('%Y-%m-%d')} a {dados_teste['Date'].max().strftime('%Y-%m-%d')})")

    # Extrair features e targets
    X_train = dados_treino[feature_cols]
    X_test = dados_teste[feature_cols]
    y_train = y_ordenado[mask_treino]
    y_test = y_ordenado[mask_teste]

    return X_train, X_test, y_train, y_test


def treinar_estimador_xgboost(X, y, feature_cols, dataset_completo=None):
    """
    Treina um estimador XGBoost para predizer variação percentual da média OHLC
    Usa divisão temporal configurável para treino e teste
    """
    # Usar configurações do XGBoost
    use_scaler = config.get('xgboost.use_standard_scaler')
    model_params = config.get('xgboost.model_params').copy()

    # Configurar para regressão
    model_params['objective'] = 'reg:squarederror'
    model_params['eval_metric'] = 'rmse'

    # Parâmetros adicionais para garantir reprodutibilidade completa
    model_params['random_state'] = RANDOM_SEED
    model_params['seed'] = RANDOM_SEED
    model_params['deterministic_histogram'] = True
    model_params['single_precision_histogram'] = True
    # Parâmetros adicionais para XGBoost determinístico
    model_params['subsample'] = 1.0  # Usar todos os dados (sem amostragem aleatória)
    model_params['colsample_bytree'] = 1.0  # Usar todas as features (sem amostragem aleatória)
    model_params['colsample_bylevel'] = 1.0  # Usar todas as features por nível
    model_params['colsample_bynode'] = 1.0  # Usar todas as features por nó

    # Fazer divisão temporal se dataset_completo estiver disponível
    if dataset_completo is not None and 'Date' in dataset_completo.columns:
        X_train, X_test, y_train, y_test = dividir_dados_temporal_regressao(
            dataset_completo, feature_cols, y
        )

    # Normalizar features se configurado
    if use_scaler:
        # StandardScaler com seed fixa para reprodutibilidade
        scaler = StandardScaler()
        # Garantir ordem determinística dos dados antes do fit
        X_train_sorted = X_train.sort_index()
        X_train_scaled = scaler.fit_transform(X_train_sorted)
        X_test_scaled = scaler.transform(X_test)
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=feature_cols, index=X_train_sorted.index)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=feature_cols, index=X_test.index)
        # Restaurar ordem original
        X_train_scaled = X_train_scaled.reindex(X_train.index)
    else:
        scaler = None
        X_train_scaled = X_train
        X_test_scaled = X_test

    # NOVO: Calcular pesos para o treinamento se configurado
    use_weighted_training = config.get('xgboost.estimator.use_weighted_training')
    weights = None
    if use_weighted_training:
        print("\n⚖️  Usando pesos no treinamento para amenizar o impacto de grandes variações.")
        # Pesos inversamente proporcionais ao módulo do target para amenizar o efeito de grandes variações
        # Adicionar um pequeno epsilon para evitar divisão por zero
        weights = 1/(np.exp(-(y_train**2)/8)+1e-6)
        
        # Normalizar os pesos para que a soma não altere drasticamente a magnitude do gradiente
        weights = weights/np.mean(weights)


    # Treinar estimador XGBoost
    print("\n🚀 Treinando estimador XGBoost...")
    print(f"   • Target: Variação % OHLC (regressão)")
    print(f"   • Função de perda: RMSE")

    modelo = xgb.XGBRegressor(**model_params)
    # Passar os pesos para o método fit (se existirem)
    modelo.fit(X_train_scaled, y_train, sample_weight=weights)

    # Fazer predições
    y_pred = modelo.predict(X_test_scaled)

    # Calcular métricas de regressão
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)

    print(f"   • RMSE: {rmse:.4f}")
    print(f"   • MAE: {mae:.4f}")
    print(f"   • R²: {r2:.4f}")

    resultados = {
        'modelo': modelo,
        'scaler': scaler,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'y_test': y_test,
        'y_pred': y_pred,
        'feature_importance': modelo.feature_importances_
    }

    return resultados, feature_cols


def criar_grafico_feature_importance_simples(feature_importance, feature_cols, figures_dir):
    """
    Cria apenas o gráfico de importância das features (para quando não temos dados de teste)
    """
    fig, ax = plt.subplots(figsize=(12, 10))

    # Criar DataFrame com importâncias
    feature_importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': feature_importance
    }).sort_values('importance', ascending=True).tail(20)  # Top 20

    # Criar gráfico de barras horizontal
    bars = ax.barh(range(len(feature_importance_df)), feature_importance_df['importance'],
                   color='steelblue', alpha=0.7)

    # Configurações do gráfico
    ax.set_yticks(range(len(feature_importance_df)))
    ax.set_yticklabels(feature_importance_df['feature'], fontsize=9)
    ax.set_xlabel('Importância', fontsize=12, fontweight='bold')
    ax.set_title('Top 20 Features Mais Importantes - Estimador XGBoost\n' +
                f'Total de Features: {len(feature_cols)}',
                fontsize=14, fontweight='bold', pad=20)

    # Adicionar valores nas barras
    for i, bar in enumerate(bars):
        width = bar.get_width()
        ax.text(width + 0.001, bar.get_y() + bar.get_height()/2,
                f'{width:.3f}', ha='left', va='center', fontsize=8)

    ax.grid(True, alpha=0.3, axis='x')
    plt.tight_layout()

    # Salvar gráfico
    plt.savefig(os.path.join(figures_dir, 'feature_importance_estimador.png'),
                dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 Gráfico de importância das features salvo")

def criar_graficos_desempenho_estimador(resultados_modelo, feature_cols):
    """
    Cria gráficos para avaliar o desempenho do estimador XGBoost
    """
    print(f"\n📊 Criando gráficos de desempenho do estimador...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/xgboost_estimador_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files'):
        for arquivo in os.listdir(figures_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(figures_dir, arquivo))
        print(f"🗑️ Figuras antigas removidas de {figures_dir}")

    # Extrair dados dos resultados
    rmse = resultados_modelo['rmse']
    mae = resultados_modelo['mae']
    r2 = resultados_modelo['r2']
    feature_importance = resultados_modelo['feature_importance']

    # Verificar se temos dados de teste (pode não ter se modelo foi carregado do cache)
    y_test = resultados_modelo.get('y_test')
    y_pred = resultados_modelo.get('y_pred')

    if y_test is None or y_pred is None:
        print("⚠️ Dados de teste não disponíveis (modelo carregado do cache)")
        print("📊 Criando apenas gráfico de importância das features...")

        # Criar apenas gráfico de importância das features
        criar_grafico_feature_importance_simples(feature_importance, feature_cols, figures_dir)
        return

    # Configurar estilo dos gráficos
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 10

    # 1. Gráfico: Valores Reais vs Estimados (Scatter Plot)
    fig, ax = plt.subplots(figsize=(10, 8))

    # Scatter plot
    ax.scatter(y_test, y_pred, alpha=0.6, color='steelblue', s=20)

    # Linha de referência perfeita (y = x)
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Predição Perfeita')

    # Configurações do gráfico
    ax.set_xlabel('Variação % Real', fontsize=12)
    ax.set_ylabel('Variação % Estimada', fontsize=12)
    ax.set_title('Estimador XGBoost: Variação % Real vs Estimada\n' +
                f'R² = {r2:.4f} | RMSE = {rmse:.2f}% | MAE = {mae:.2f}%',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Adicionar estatísticas no gráfico
    ax.text(0.05, 0.95, f'Pontos: {len(y_test)}\nR²: {r2:.4f}\nRMSE: {rmse:.2f}\nMAE: {mae:.2f}',
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'valores_reais_vs_estimados.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Gráfico: Série Temporal dos Últimos 100 Pontos
    fig, ax = plt.subplots(figsize=(14, 8))

    # Pegar últimos 100 pontos para visualização
    n_pontos = min(100, len(y_test))
    indices = range(len(y_test) - n_pontos, len(y_test))

    ax.plot(indices, y_test.iloc[-n_pontos:], 'o-', color='blue', linewidth=2,
            markersize=4, label='Valores Reais', alpha=0.8)
    ax.plot(indices, y_pred[-n_pontos:], 's-', color='red', linewidth=2,
            markersize=4, label='Valores Estimados', alpha=0.8)

    ax.set_xlabel('Índice da Amostra', fontsize=12)
    ax.set_ylabel('Variação % da Média OHLC', fontsize=12)
    ax.set_title(f'Estimador XGBoost: Série Temporal (Últimos {n_pontos} Pontos)\n' +
                f'Comparação entre Variações % Reais e Estimadas', fontsize=14, fontweight='bold')
    ax.legend(loc='upper left')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'serie_temporal_comparacao.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 3. Gráfico: Distribuição dos Erros
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # Calcular erros
    erros = y_pred - y_test

    # Para erros percentuais, como y_test já é em %, o erro também é em %
    # Não precisamos dividir por y_test novamente
    erros_percentuais = erros.copy()

    # Remover valores infinitos e NaN
    erros_clean = erros[np.isfinite(erros)]
    erros_percentuais_clean = erros_percentuais[np.isfinite(erros_percentuais)]

    # Histograma dos erros absolutos
    if len(erros_clean) > 0:
        ax1.hist(erros_clean, bins=50, alpha=0.7, color='steelblue', edgecolor='black')
        ax1.axvline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
        ax1.set_xlabel('Erro (Estimado - Real) (%)', fontsize=12)
        ax1.set_ylabel('Frequência', fontsize=12)
        ax1.set_title('Distribuição dos Erros Absolutos', fontsize=12, fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
    else:
        ax1.text(0.5, 0.5, 'Dados insuficientes', ha='center', va='center', transform=ax1.transAxes)

    # Histograma dos erros percentuais (mesmo que absolutos neste caso)
    if len(erros_percentuais_clean) > 0:
        ax2.hist(erros_percentuais_clean, bins=50, alpha=0.7, color='orange', edgecolor='black')
        ax2.axvline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
        ax2.set_xlabel('Erro de Variação (%)', fontsize=12)
        ax2.set_ylabel('Frequência', fontsize=12)
        ax2.set_title('Distribuição dos Erros de Variação', fontsize=12, fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Adicionar estatísticas
        mean_error = np.mean(erros_percentuais_clean)
        std_error = np.std(erros_percentuais_clean)
        ax2.text(0.02, 0.98, f'Média: {mean_error:.2f}%\nDesvio: {std_error:.2f}%',
                transform=ax2.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    else:
        ax2.text(0.5, 0.5, 'Dados insuficientes', ha='center', va='center', transform=ax2.transAxes)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'distribuicao_erros.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 4. Gráfico: Importância das Features (Top 20)
    fig, ax = plt.subplots(figsize=(12, 10))

    # Criar DataFrame com importâncias
    feature_importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': feature_importance
    }).sort_values('importance', ascending=True).tail(20)  # Top 20

    # Gráfico de barras horizontal
    bars = ax.barh(range(len(feature_importance_df)), feature_importance_df['importance'],
                   color='steelblue', alpha=0.8)

    # Configurações
    ax.set_yticks(range(len(feature_importance_df)))
    ax.set_yticklabels(feature_importance_df['feature'], fontsize=10)
    ax.set_xlabel('Importância', fontsize=12)
    ax.set_title('Top 20 Features Mais Importantes\nEstimador XGBoost', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3, axis='x')

    # Adicionar valores nas barras
    for i, bar in enumerate(bars):
        width = bar.get_width()
        ax.text(width + 0.001, bar.get_y() + bar.get_height()/2,
                f'{width:.3f}', ha='left', va='center', fontsize=8)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'importancia_features.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 5. NOVO: Gráfico de Distribuição do Target (y_test) com ajuste Gaussiano
    fig, ax = plt.subplots(figsize=(12, 7))

    # Calcular parâmetros da distribuição Gaussiana
    mu, sigma = norm.fit(y_test)

    # Histograma dos valores reais do target
    ax.hist(y_test, bins=100, density=True, color='skyblue', edgecolor='black', alpha=0.7, label='Distribuição Real')

    # Plotar a curva de ajuste Gaussiano (PDF)
    xmin, xmax = plt.xlim()
    x = np.linspace(xmin, xmax, 100)
    p = norm.pdf(x, mu, sigma)
    ax.plot(x, p, 'k', linewidth=2, label=f'Ajuste Gaussiano (μ={mu:.2f}, σ={sigma:.2f})')

    # Linhas de referência para média e mediana
    ax.axvline(y_test.mean(), color='red', linestyle='--', linewidth=2, label=f'Média (μ): {mu:.2f}%')
    ax.axvline(y_test.median(), color='green', linestyle=':', linewidth=2, label=f'Mediana: {y_test.median():.2f}%')

    # Configurações do gráfico
    ax.set_title('Distribuição do Target (Variação % Real) com Ajuste Gaussiano', fontsize=14, fontweight='bold')
    ax.set_xlabel('Variação % da Média OHLC', fontsize=12)
    ax.set_ylabel('Densidade de Probabilidade', fontsize=12)
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'distribuicao_target.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"   ✅ Gráficos de desempenho salvos em: {figures_dir}/")
    print(f"      • valores_reais_vs_estimados.png")
    print(f"      • serie_temporal_comparacao.png")
    print(f"      • distribuicao_erros.png")
    print(f"      • importancia_features.png")
    print(f"      • distribuicao_target.png (NOVO)")


def criar_graficos_sinais_individuais(acoes_com_predicoes, max_acoes=6):
    """
    Cria gráficos individuais para ações com sinais de compra/venda
    Mostra preço histórico, valor estimado e sinais
    """
    print(f"\n📈 Criando gráficos individuais de sinais...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/xgboost_estimador_analysis'
    individual_dir = os.path.join(figures_dir, 'individual_signals')
    os.makedirs(individual_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files'):
        for arquivo in os.listdir(individual_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(individual_dir, arquivo))

    # Coletar ações com sinais
    acoes_com_sinais = []
    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ultimo_dia = dados.tail(1).iloc[0]
            if ultimo_dia.get('Pred_Compra', 0) == 1 or ultimo_dia.get('Pred_Venda', 0) == 1:
                acoes_com_sinais.append((ticker, dados))

    # Limitar número de gráficos
    acoes_selecionadas = acoes_com_sinais[:max_acoes]

    if not acoes_selecionadas:
        print(f"   ⚠️ Nenhuma ação com sinais encontrada")
        return

    print(f"   📊 Criando gráficos para {len(acoes_selecionadas)} ações com sinais...")

    for ticker, dados in acoes_selecionadas:
        ticker_clean = ticker.replace('.SA', '')

        # Pegar últimos 60 dias para visualização
        dados_recentes = dados.tail(60)

        if len(dados_recentes) < 10:
            continue

        # Criar figura com subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

        # Gráfico 1: Preço e Sinais
        ax1.plot(dados_recentes.index, dados_recentes['Media_OHLC'],
                'b-', linewidth=2, label='Preço (Média OHLC)', alpha=0.8)

        # Mostrar valor estimado no último dia
        ultimo_dia = dados_recentes.tail(1)
        if not ultimo_dia.empty and 'Valor_Estimado' in ultimo_dia.columns:
            valor_estimado = ultimo_dia['Valor_Estimado'].iloc[0]
            if valor_estimado > 0:
                ax1.plot(ultimo_dia.index, [valor_estimado],
                        'ro', markersize=10, label=f'Valor Estimado: R$ {valor_estimado:.2f}')

        # Marcar sinais de compra e venda
        sinais_compra = dados_recentes[dados_recentes.get('Pred_Compra', 0) == 1]
        sinais_venda = dados_recentes[dados_recentes.get('Pred_Venda', 0) == 1]

        if len(sinais_compra) > 0:
            ax1.scatter(sinais_compra.index, sinais_compra['Media_OHLC'],
                       color='green', marker='^', s=100, label='Sinal Compra', zorder=5)

        if len(sinais_venda) > 0:
            ax1.scatter(sinais_venda.index, sinais_venda['Media_OHLC'],
                       color='red', marker='v', s=100, label='Sinal Venda', zorder=5)

        # Configurações do gráfico 1
        ax1.set_title(f'{ticker_clean} - Preço e Sinais do Estimador XGBoost',
                     fontsize=14, fontweight='bold')
        ax1.set_ylabel('Preço (R$)', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)

        # Gráfico 2: Volume
        ax2.bar(dados_recentes.index, dados_recentes['Volume'],
               alpha=0.6, color='steelblue', label='Volume')

        # Destacar volume nos dias de sinais
        if len(sinais_compra) > 0:
            ax2.bar(sinais_compra.index, sinais_compra['Volume'],
                   alpha=0.8, color='green', label='Volume - Compra')
        if len(sinais_venda) > 0:
            ax2.bar(sinais_venda.index, sinais_venda['Volume'],
                   alpha=0.8, color='red', label='Volume - Venda')

        ax2.set_title('Volume de Negociação', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Volume', fontsize=12)
        ax2.set_xlabel('Data', fontsize=12)
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)

        # Rotacionar labels das datas
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

        # Adicionar informações do último sinal
        if not ultimo_dia.empty:
            info_text = f"Último Dia: {ultimo_dia.index[0].strftime('%d/%m/%Y')}\n"
            info_text += f"Preço: R$ {ultimo_dia['Media_OHLC'].iloc[0]:.2f}\n"

            if 'Valor_Estimado' in ultimo_dia.columns:
                valor_est = ultimo_dia['Valor_Estimado'].iloc[0]
                pct_change = ultimo_dia.get('Pct_Change_Estimado', [0]).iloc[0]
                info_text += f"Estimado: R$ {valor_est:.2f} ({pct_change:+.2f}%)\n"

            sinal_tipo = ultimo_dia.get('Sinal_Tipo', ['SEM_SINAL']).iloc[0]
            info_text += f"Sinal: {sinal_tipo}"

            ax1.text(0.02, 0.98, info_text, transform=ax1.transAxes,
                    verticalalignment='top', fontsize=10,
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        # Salvar gráfico
        arquivo_grafico = os.path.join(individual_dir, f'{ticker_clean}_sinais_estimador.png')
        plt.savefig(arquivo_grafico, dpi=300, bbox_inches='tight')
        plt.close()

    print(f"   ✅ Gráficos individuais salvos em: {individual_dir}/")
    print(f"      • {len(acoes_selecionadas)} gráficos de ações com sinais")


def aplicar_predicoes_estimador(acoes_dados, resultados_modelo, feature_cols):
    """
    Aplica as predições do estimador XGBoost aos dados de cada ação
    Converte predições de variação percentual de volta para valores OHLC
    Gera sinais de compra/venda baseados no valor estimado vs valor anterior
    """
    modelo = resultados_modelo['modelo']
    scaler = resultados_modelo['scaler']
    threshold = config.get('xgboost.features.pct_threshold')  # Usar mesmo threshold do classificador

    acoes_com_predicoes = {}

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            dados_copy = dados.copy()

            # Verificar se todas as features existem
            features_disponiveis = [col for col in feature_cols if col in dados_copy.columns]
            if len(features_disponiveis) != len(feature_cols):
                print(f"     ⚠️ Features faltando para {ticker}: {len(features_disponiveis)}/{len(feature_cols)}")
                continue

            # Pegar apenas o último dia disponível (sem NaN nas features essenciais)
            X_all = dados_copy[feature_cols].dropna()
            if len(X_all) == 0:
                continue

            # Usar apenas o último dia para predição (dia atual)
            X_ultimo_dia = X_all.tail(1)

            # Aplicar scaler se necessário
            if scaler is not None:
                X_scaled = scaler.transform(X_ultimo_dia)
                X_scaled = pd.DataFrame(X_scaled, columns=feature_cols, index=X_ultimo_dia.index)
            else:
                X_scaled = X_ultimo_dia

            # Fazer predição da VARIAÇÃO PERCENTUAL (do dia anterior para o dia estimado)
            pct_change_estimado_modelo = modelo.predict(X_scaled)[0]

            # Obter valor do dia ATUAL (HOJE) para a decisão
            preco_atual = dados_copy['Media_OHLC'].iloc[-1]

            # CORREÇÃO: A base para a predição de amanhã deve ser o preço de HOJE.
            # O modelo aprende a prever a variação do dia seguinte, então aplicamos
            # essa variação ao preço mais recente conhecido.
            valor_base_para_predicao = preco_atual

            # CONVERTER DE VOLTA PARA VALOR OHLC ESTIMADO
            # Se pct_change_estimado_modelo = 5%, e preco_atual = 10, então valor_estimado = 10 * (1 + 0.05) = 10.5
            if valor_base_para_predicao > 0:
                valor_estimado = valor_base_para_predicao * (1 + pct_change_estimado_modelo / 100)
            else:
                valor_estimado = 0

            # [NOVA LÓGICA] Calcular a variação percentual para a DECISÃO e EXIBIÇÃO
            # Compara o valor estimado com o preço ATUAL
            if preco_atual > 0:
                pct_change_final = ((valor_estimado / preco_atual) - 1) * 100
            else:
                pct_change_final = 0.0

            # Gerar sinais baseados no threshold, usando a nova variação percentual
            if pct_change_final > threshold:
                sinal_compra = 1
                sinal_venda = 0
                sinal_tipo = "COMPRA"
            elif pct_change_final < -threshold:
                sinal_compra = 0
                sinal_venda = 1
                sinal_tipo = "VENDA"
            else:
                sinal_compra = 0
                sinal_venda = 0
                sinal_tipo = "SEM_SINAL"

            # Adicionar predições aos dados (apenas para o último dia)
            dados_copy.loc[X_ultimo_dia.index, 'Valor_Estimado'] = valor_estimado
            # Salvar a variação final, que é mais intuitiva para o usuário
            dados_copy.loc[X_ultimo_dia.index, 'Pct_Change_Estimado'] = pct_change_final
            dados_copy.loc[X_ultimo_dia.index, 'Pred_Compra'] = sinal_compra
            dados_copy.loc[X_ultimo_dia.index, 'Pred_Venda'] = sinal_venda
            dados_copy.loc[X_ultimo_dia.index, 'Sinal_Tipo'] = sinal_tipo

            # Preencher NaN com valores padrão
            dados_copy['Valor_Estimado'] = dados_copy['Valor_Estimado'].fillna(0.0)
            dados_copy['Pct_Change_Estimado'] = dados_copy['Pct_Change_Estimado'].fillna(0.0)
            dados_copy['Pred_Compra'] = dados_copy['Pred_Compra'].fillna(0).astype(int)
            dados_copy['Pred_Venda'] = dados_copy['Pred_Venda'].fillna(0).astype(int)
            dados_copy['Sinal_Tipo'] = dados_copy['Sinal_Tipo'].fillna("SEM_SINAL")

            acoes_com_predicoes[ticker] = dados_copy

    print(f"   ✅ Predições aplicadas a {len(acoes_com_predicoes)} ações")
    print(f"   📊 Modelo prediz variação % e converte para valor OHLC")
    print(f"   📊 Threshold utilizado: {threshold}% (mesmo do classificador)")

    return acoes_com_predicoes


def carregar_carteira_atual():
    """
    Carrega a carteira atual do arquivo carteira.csv
    Retorna um dicionário com ticker -> quantidade atual
    """
    try:
        carteira_df = pd.read_csv('carteira.csv')

        # Calcular posição atual de cada ticker
        carteira_atual = {}
        for _, row in carteira_df.iterrows():
            ticker = row['ticker']
            quantidade = row['quantidade']

            if ticker in carteira_atual:
                carteira_atual[ticker] += quantidade
            else:
                carteira_atual[ticker] = quantidade

        # Filtrar apenas tickers com quantidade > 0
        carteira_atual = {ticker: qtd for ticker, qtd in carteira_atual.items() if qtd > 0}

        print(f"📋 Carteira atual carregada: {len(carteira_atual)} posições ativas")
        for ticker, qtd in carteira_atual.items():
            ticker_clean = ticker.replace('.SA', '')
            print(f"   • {ticker_clean}: {qtd} ações")

        return carteira_atual

    except FileNotFoundError:
        print(f"⚠️ Arquivo carteira.csv não encontrado - mostrando todos os sinais de venda")
        return {}
    except Exception as e:
        print(f"⚠️ Erro ao carregar carteira: {e} - mostrando todos os sinais de venda")
        return {}


def imprimir_recomendacoes_estimador(acoes_com_predicoes):
    """
    Imprime recomendações de compra e venda baseadas no estimador
    Sinais de venda apenas para ações na carteira atual
    Segue o mesmo formato do classificador XGBoost
    """
    print(f"\n📊 ESTRATÉGIA DE TRADING - ESTIMADOR XGBOOST")
    print(f"=" * 60)

    # Verificar data mais recente disponível
    data_mais_recente = None
    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ultima_data = dados.index.max()
            if data_mais_recente is None or ultima_data > data_mais_recente:
                data_mais_recente = ultima_data

    if data_mais_recente:
        print(f"📅 Análise baseada nos dados até: {data_mais_recente.strftime('%d/%m/%Y (%A)')}")
        print(f"🤖 Sinais baseados nas estimativas do modelo XGBoost (regressão)")
        print(f"🎯 Threshold: {config.get('xgboost.features.pct_threshold')}%")

    # Carregar carteira atual para filtrar sinais de venda
    carteira_atual = carregar_carteira_atual()

    # Coletar sinais
    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ticker_clean = ticker.replace('.SA', '')
            ultimo_dia = dados.tail(1).iloc[0]

            # Verificar se há sinais
            if ultimo_dia.get('Pred_Compra', 0) == 1:
                sinais_compra.append({
                    'ticker_clean': ticker_clean,
                    'nome': ticker_clean,  # Nome simplificado
                    'preco': ultimo_dia.get('Media_OHLC', 0),
                    'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                    'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                    'volume': ultimo_dia.get('Volume', 0),
                    'volatilidade': ultimo_dia.get('Volatilidade', 0),
                    'spread': ultimo_dia.get('Spread', 0),
                    'data': ultimo_dia.name
                })
            elif ultimo_dia.get('Pred_Venda', 0) == 1:
                # FILTRO: Apenas mostrar sinais de venda para ações na carteira
                if ticker in carteira_atual and carteira_atual[ticker] > 0:
                    sinais_venda.append({
                        'ticker_clean': ticker_clean,
                        'nome': ticker_clean,  # Nome simplificado
                        'preco': ultimo_dia.get('Media_OHLC', 0),
                        'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                        'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'quantidade_carteira': carteira_atual[ticker]
                    })

    # Exibir sinais de venda PRIMEIRO (ordenados por maior queda estimada)
    # APENAS para ações na carteira atual
    if sinais_venda:
        print(f"\n🔴 SINAIS DE VENDA ({len(sinais_venda)} ações na carteira):")
        print("-" * 60)
        # Ordenar por maior queda estimada (mais negativo primeiro)
        for sinal in sorted(sinais_venda, key=lambda x: x['pct_change_estimado']):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            quantidade = sinal.get('quantidade_carteira', 0)
            print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]}) - {quantidade} ações na carteira")
            print(f"      💰 Preço atual: R$ {sinal['preco']:.2f}")
            print(f"      🎯 Valor estimado: R$ {sinal['valor_estimado']:.2f} ({sinal['pct_change_estimado']:+.2f}%)")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🤖 Estimador prevê queda no preço")
            print()
    else:
        print(f"\n🔴 SINAIS DE VENDA: Nenhuma ação na carteira com sinal de venda")
        print("-" * 60)

    # Exibir sinais de compra DEPOIS (ordenados por maior alta estimada)
    if sinais_compra:
        print(f"\n🟢 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print("-" * 60)
        # Ordenar por maior alta estimada (mais positivo primeiro)
        for sinal in sorted(sinais_compra, key=lambda x: x['pct_change_estimado'], reverse=True):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            print(f"   📈 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
            print(f"      💰 Preço atual: R$ {sinal['preco']:.2f}")
            print(f"      🎯 Valor estimado: R$ {sinal['valor_estimado']:.2f} ({sinal['pct_change_estimado']:+.2f}%)")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🤖 Estimador prevê alta no preço")
            print()

    # Resumo final
    print("📋 RESUMO DOS SINAIS:")
    print(f"   🔴 Venda: {len(sinais_venda)} ações na carteira (ordenadas por maior queda estimada)")
    print(f"   🟢 Compra: {len(sinais_compra)} ações (ordenadas por maior alta estimada)")
    print(f"   📊 Total de sinais: {len(sinais_compra) + len(sinais_venda)} ações")
    print(f"   🎯 Threshold: {config.get('xgboost.features.pct_threshold')}%")
    if carteira_atual:
        print(f"   📋 Carteira atual: {len(carteira_atual)} posições ativas")


def salvar_resultados_estimador(acoes_com_predicoes, resultados_modelo, feature_cols):
    """
    Salva os resultados do estimador XGBoost em arquivos CSV
    Sinais de venda apenas para ações na carteira atual
    Segue o mesmo formato do classificador
    """
    setup_environment()

    # Criar diretório de saída
    output_dir = 'results/csv/xgboost_estimador_analysis'
    individual_dir = os.path.join(output_dir, 'individual_stocks')
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(individual_dir, exist_ok=True)

    # Limpar arquivos antigos
    if config.get('output.clean_old_files'):
        for arquivo in os.listdir(output_dir):
            if arquivo.endswith('.csv'):
                os.remove(os.path.join(output_dir, arquivo))
        for arquivo in os.listdir(individual_dir):
            if arquivo.endswith('.csv'):
                os.remove(os.path.join(individual_dir, arquivo))
        print(f"🗑️ Arquivos antigos removidos de {output_dir}")

    # Carregar carteira atual para filtrar sinais de venda
    carteira_atual = carregar_carteira_atual()

    # Preparar dados para salvar
    dados_completos = []
    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_com_predicoes.items():
        ticker_clean = ticker.replace('.SA', '')

        # Preparar dados para salvar (últimos 30 dias + colunas essenciais)
        colunas_salvar = [
            'Media_OHLC', 'Volume', 'Spread', 'Volatilidade',
            'Valor_Estimado', 'Pct_Change_Estimado', 'Pred_Compra', 'Pred_Venda', 'Sinal_Tipo'
        ]

        # Filtrar apenas colunas que existem
        colunas_existentes = [col for col in colunas_salvar if col in dados.columns]
        dados_selecionados = dados[colunas_existentes].tail(30).copy()

        # Adicionar informações do ticker
        dados_selecionados['Ticker'] = ticker_clean
        dados_selecionados['Data'] = dados_selecionados.index.strftime('%Y-%m-%d')

        # Reorganizar colunas
        cols_ordem = ['Ticker', 'Data'] + [col for col in dados_selecionados.columns if col not in ['Ticker', 'Data']]
        dados_selecionados = dados_selecionados[cols_ordem]

        # Salvar arquivo individual
        arquivo_individual = os.path.join(individual_dir, f'estimador_{ticker_clean}.csv')
        dados_selecionados.to_csv(arquivo_individual, index=False)

        dados_completos.append(dados_selecionados)

        # Coletar sinais para resumo (último dia)
        ultimo_dia = dados.tail(1).iloc[0]
        if ultimo_dia.get('Pred_Compra', 0) == 1:
            sinais_compra.append({
                'ticker': ticker_clean,
                'data': ultimo_dia.name.strftime('%Y-%m-%d'),
                'preco_atual': ultimo_dia.get('Media_OHLC', 0),
                'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                'volume': ultimo_dia.get('Volume', 0),
                'volatilidade': ultimo_dia.get('Volatilidade', 0)
            })
        elif ultimo_dia.get('Pred_Venda', 0) == 1:
            # FILTRO: Apenas salvar sinais de venda para ações na carteira
            if ticker in carteira_atual and carteira_atual[ticker] > 0:
                sinais_venda.append({
                    'ticker': ticker_clean,
                    'data': ultimo_dia.name.strftime('%Y-%m-%d'),
                    'preco_atual': ultimo_dia.get('Media_OHLC', 0),
                    'valor_estimado': ultimo_dia.get('Valor_Estimado', 0),
                    'pct_change_estimado': ultimo_dia.get('Pct_Change_Estimado', 0),
                    'volume': ultimo_dia.get('Volume', 0),
                    'volatilidade': ultimo_dia.get('Volatilidade', 0),
                    'quantidade_carteira': carteira_atual[ticker]
                })

    # Combinar todos os dados
    if dados_completos:
        df_final = pd.concat(dados_completos, ignore_index=True)
        df_final = df_final.sort_values(['Ticker', 'Data'])

        # Salvar arquivo completo principal
        csv_completo_path = os.path.join(output_dir, 'resultados_estimador_completo.csv')
        df_final.to_csv(csv_completo_path, index=False)

    # Salvar resumo de sinais de venda (ordenados por maior queda estimada)
    if sinais_venda:
        df_venda = pd.DataFrame(sinais_venda)
        df_venda = df_venda.sort_values('pct_change_estimado', ascending=True)  # Maior queda primeiro
        arquivo_venda = os.path.join(output_dir, 'sinais_venda_estimador.csv')
        df_venda.to_csv(arquivo_venda, index=False)

    # Salvar resumo de sinais de compra (ordenados por maior alta estimada)
    if sinais_compra:
        df_compra = pd.DataFrame(sinais_compra)
        df_compra = df_compra.sort_values('pct_change_estimado', ascending=False)  # Maior alta primeiro
        arquivo_compra = os.path.join(output_dir, 'sinais_compra_estimador.csv')
        df_compra.to_csv(arquivo_compra, index=False)

    # Salvar importância das features
    feature_importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': resultados_modelo['feature_importance']
    }).sort_values('importance', ascending=False)

    arquivo_importance = os.path.join(output_dir, 'feature_importance_estimador.csv')
    feature_importance_df.to_csv(arquivo_importance, index=False)

    # Salvar métricas do modelo
    metricas = {
        'RMSE': resultados_modelo['rmse'],
        'MAE': resultados_modelo['mae'],
        'R²': resultados_modelo['r2']
    }

    metricas_df = pd.DataFrame([metricas])
    arquivo_metricas = os.path.join(output_dir, 'metricas_estimador.csv')
    metricas_df.to_csv(arquivo_metricas, index=False)

    print(f"\n💾 Resultados salvos em: {output_dir}/")
    print(f"   • Dados completos: resultados_estimador_completo.csv")
    print(f"   • Sinais de VENDA: sinais_venda_estimador.csv ({len(sinais_venda)} ações)")
    print(f"   • Sinais de COMPRA: sinais_compra_estimador.csv ({len(sinais_compra)} ações)")
    print(f"   • Importância features: feature_importance_estimador.csv")
    print(f"   • Métricas modelo: metricas_estimador.csv")
    print(f"   • Dados individuais: individual_stocks/ ({len(dados_completos)} ações)")


def main():
    """
    Função principal do estimador XGBoost
    """
    print("🎯 ESTIMADOR XGBOOST - PREDIÇÃO DE VALORES DA MÉDIA OHLC")
    print("=" * 80)

    # Configurar ambiente
    setup_environment()

    # Carregar ações diversificadas
    print("\n📋 Carregando ações diversificadas...")
    todas_acoes = carregar_acoes_diversificadas()

    if not todas_acoes:
        print("❌ Nenhuma ação encontrada!")
        return

    # Baixar dados de todas as ações
    print(f"\n📊 Baixando dados de {len(todas_acoes)} ações...")
    # Converter formato de tuplas para compatibilidade com cache unificado
    todas_acoes_com_origem = [(ticker, nome, 'diversificacao') for ticker, nome in todas_acoes]
    acoes_dados = baixar_dados_todas_acoes_unificado(todas_acoes_com_origem)

    if not acoes_dados:
        print("❌ Falha ao baixar dados das ações!")
        return

    # Calcular features para todas as ações
    print(f"\n🔧 Calculando features para {len(acoes_dados)} ações...")
    for ticker, dados in acoes_dados.items():
        if dados is not None:
            acoes_dados[ticker] = calcular_features_e_sinais(dados, ticker)

    # Verificar se modelo estimador já foi treinado hoje (considerando force_training)
    modelo_dir = 'results/models/xgboost_estimador_analysis'
    modelo_nome = 'modelo_estimador.pkl'
    os.makedirs(modelo_dir, exist_ok=True)

    force_training = config.get('xgboost', {}).get('cache', {}).get('force_training', False)
    if force_training:
        print(f"🔄 Forçando treinamento do modelo estimador (force_training=True)")
        modelo_treinado_hoje, modelo_path = False, None
    else:
        modelo_treinado_hoje, modelo_path = verificar_modelo_estimador_treinado_hoje(modelo_dir, modelo_nome)

    if modelo_treinado_hoje:
        # Carregar modelo existente
        print(f"\n📦 Carregando modelo estimador treinado hoje...")
        resultados_modelo = carregar_modelo_estimador_existente(modelo_path)

        if resultados_modelo is None:
            print("❌ Erro ao carregar modelo estimador existente, retreinando...")
            modelo_treinado_hoje = False
        else:
            feature_cols = resultados_modelo.get('feature_cols', [])
            print(f"✅ Modelo estimador carregado com sucesso!")

    if not modelo_treinado_hoje:
        # Preparar dataset para regressão
        print(f"\n📈 Preparando dataset de regressão...")
        X, y, feature_cols, dataset_completo = preparar_dataset_regressao(acoes_dados)

        # Treinar estimador
        print(f"\n🚀 Treinando estimador XGBoost...")
        resultados_modelo, feature_cols = treinar_estimador_xgboost(X, y, feature_cols, dataset_completo)

        # Salvar modelo treinado
        print(f"\n💾 Salvando modelo estimador...")
        salvar_modelo_estimador(resultados_modelo, feature_cols)

    # Aplicar predições e gerar sinais
    print(f"\n🎯 Aplicando predições e gerando sinais...")
    acoes_com_predicoes = aplicar_predicoes_estimador(acoes_dados, resultados_modelo, feature_cols)

    # Mostrar recomendações (mesmo formato do classificador)
    imprimir_recomendacoes_estimador(acoes_com_predicoes)

    # Criar gráficos de desempenho
    print(f"\n📊 Criando gráficos de desempenho...")
    criar_graficos_desempenho_estimador(resultados_modelo, feature_cols)

    # Criar gráficos individuais de sinais
    criar_graficos_sinais_individuais(acoes_com_predicoes)

    # Salvar resultados
    print(f"\n💾 Salvando resultados...")
    salvar_resultados_estimador(acoes_com_predicoes, resultados_modelo, feature_cols)

    print(f"\n✅ Estimador XGBoost executado com sucesso!")
    print(f"📁 Resultados salvos em:")
    print(f"   • CSV: results/csv/xgboost_estimador_analysis/")
    print(f"   • Gráficos: results/figures/xgboost_estimador_analysis/")


if __name__ == "__main__":
    main()
